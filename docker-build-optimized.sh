#!/bin/bash

# Optimized Docker build script with better caching

set -e

echo "🚀 Starting optimized Docker builds..."

# Build with Build<PERSON><PERSON> for better caching
export DOCKER_BUILDKIT=1

# Function to build with cache
build_with_cache() {
    local dockerfile=$1
    local tag=$2
    local context=$3
    
    echo "📦 Building $tag..."
    docker build \
        --file "$dockerfile" \
        --tag "$tag" \
        --cache-from "$tag:latest" \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        "$context"
}

# Build backend
echo "🔧 Building backend..."
build_with_cache "apps/backend/Dockerfile" "warda-backend" "."

# Build frontend  
echo "🎨 Building frontend..."
build_with_cache "apps/frontend/Dockerfile" "warda-frontend" "."

echo "✅ All builds completed!"
echo ""
echo "💡 Tips for faster builds:"
echo "  - Use 'docker build --cache-from' to reuse layers"
echo "  - Only change source files, not dependencies"
echo "  - Use BuildKit: export DOCKER_BUILDKIT=1"

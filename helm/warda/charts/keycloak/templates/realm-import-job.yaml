apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "keycloak.fullname" . }}-realm-import
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      labels:
        {{- include "keycloak.selectorLabels" . | nindent 8 }}
        job: realm-import
    spec:
      restartPolicy: OnFailure
      containers:
        - name: import-realm
          image: {{ .Values.initContainer.image }}
          command:
            - /bin/bash
            - /scripts/import-realm.sh
          env:
            - name: KEYCLOAK_ADMIN
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KEYCLOAK_ADMIN
            - name: KEYCLOAK_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "keycloak.fullname" . }}-secrets
                  key: KE<PERSON><PERSON><PERSON><PERSON>_ADMIN_PASSWORD
            - name: KEY<PERSON><PERSON><PERSON>_SERVICE_NAME
              value: {{ include "keycloak.fullname" . }}
          volumeMounts:
            - name: init-script
              mountPath: /scripts
              readOnly: true
            - name: realm-config
              mountPath: /realm-config
              readOnly: true
      volumes:
        - name: init-script
          configMap:
            name: {{ include "keycloak.fullname" . }}-init-script
            defaultMode: 0755
        - name: realm-config
          configMap:
            name: {{ include "keycloak.fullname" . }}-realm-config

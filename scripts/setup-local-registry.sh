#!/bin/bash
set -e

echo "🐳 Setting up local Docker registry for k3s..."

# Configure Docker daemon for insecure registry
echo "🔧 Configuring Docker daemon..."
sudo mkdir -p /etc/docker
echo '{
  "insecure-registries": ["localhost:5000", "127.0.0.1:5000"],
  "registry-mirrors": []
}' | sudo tee /etc/docker/daemon.json
sudo systemctl restart docker
sleep 5

# Start local registry if not running
if ! docker ps | grep -q "registry:2"; then
    echo "📦 Starting local Docker registry on port 5000..."
    docker run -d -p 127.0.0.1:5000:5000 --restart=always --name registry registry:2
    echo "✅ Local registry started"
else
    echo "✅ Local registry already running"
fi

# Configure k3s to use insecure registry
echo "🔧 Configuring k3s for local registry..."

# Create registries.yaml for k3s
sudo mkdir -p /etc/rancher/k3s
sudo tee /etc/rancher/k3s/registries.yaml > /dev/null <<EOF
mirrors:
  localhost:5000:
    endpoint:
      - "http://localhost:5000"
configs:
  localhost:5000:
    tls:
      insecure_skip_verify: true
EOF

echo "✅ k3s registry configuration created"

# Restart k3s to pick up the new configuration
echo "🔄 Restarting k3s to apply registry configuration..."
sudo systemctl restart k3s

# Wait for k3s to be ready
echo "⏳ Waiting for k3s to be ready..."
sleep 10
kubectl wait --for=condition=Ready nodes --all --timeout=60s

echo "🎉 Local registry setup complete!"
echo ""
echo "📋 Registry Information:"
echo "  Registry URL: localhost:5000"
echo "  Web UI: http://localhost:5000/v2/_catalog"
echo ""
echo "💡 Usage:"
echo "  docker tag myimage:latest localhost:5000/myimage:latest"
echo "  docker push localhost:5000/myimage:latest"
echo ""
echo "🚀 You can now run your GitHub Actions workflow!"

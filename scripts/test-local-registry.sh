#!/bin/bash
set -e

echo "🧪 Testing local Docker registry..."

# Test if registry is accessible
echo "📡 Testing registry connectivity..."
if curl -s http://127.0.0.1:5000/v2/ | grep -q "{}"; then
    echo "✅ Registry is accessible"
else
    echo "❌ Registry is not accessible"
    echo "Debug info:"
    docker ps | grep registry || echo "No registry container"
    netstat -tlnp | grep :5000 || echo "Port 5000 not listening"
    exit 1
fi

# Test pushing a simple image
echo "🐳 Testing image push/pull..."
docker pull hello-world:latest
docker tag hello-world:latest localhost:5000/hello-world:test
docker push localhost:5000/hello-world:test

# Test if image is in registry
echo "📋 Checking registry catalog..."
curl -s http://127.0.0.1:5000/v2/_catalog | jq .

# Test pulling from k3s
echo "🚀 Testing k3s can pull from registry..."
kubectl run test-registry --rm -i --restart=Never --image=localhost:5000/hello-world:test -- echo "Registry test successful!"

echo "🎉 Local registry test completed successfully!"

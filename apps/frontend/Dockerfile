FROM rust:1.88-slim AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev && rm -rf /var/lib/apt/lists/*

# Install Rust tools and target (cached layer)
RUN rustup target add wasm32-unknown-unknown && \
    cargo install --locked trunk && \
    cargo install wasm-bindgen-cli --version 0.2.95

ENV RUSTFLAGS='--cfg getrandom_backend="wasm_js"'
ENV CARGO_CFG_TARGET_FEATURE="atomics,bulk-memory,mutable-globals,reference-types,sign-ext,simd128"

WORKDIR /app

# Copy workspace manifests first (for dependency caching)
COPY Cargo.toml Cargo.lock ./
COPY apps/frontend/Cargo.toml ./apps/frontend/
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/migrate/Cargo.toml ./apps/migrate/
COPY crates/shared/Cargo.toml ./crates/shared/

# Create dummy source files to build dependencies
RUN mkdir -p apps/frontend/src apps/backend/src apps/migrate/src crates/shared/src && \
    echo "fn main() {}" > apps/frontend/src/main.rs && \
    echo "pub fn hello() {}" > apps/frontend/src/lib.rs && \
    echo "fn main() {}" > apps/backend/src/main.rs && \
    echo "fn main() {}" > apps/migrate/src/main.rs && \
    echo "pub fn hello() {}" > crates/shared/src/lib.rs

# Build dependencies only (cached layer)
WORKDIR /app/apps/frontend
RUN cargo build --target=wasm32-unknown-unknown --release
RUN rm -rf src ../../crates/shared/src

# Copy actual source code
COPY apps/frontend/src ./src/
COPY apps/frontend/index.html ./
COPY apps/frontend/Trunk.toml ./
COPY apps/frontend/assets ./assets/
COPY crates/shared/src ../../crates/shared/src/

# Build the actual application
RUN cargo build --target=wasm32-unknown-unknown --release
RUN trunk build --release

FROM nginx:alpine
COPY --from=builder /app/apps/frontend/dist /usr/share/nginx/html
COPY apps/frontend/nginx.conf /etc/nginx/conf.d/default.conf
COPY apps/frontend/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
EXPOSE 80
ENTRYPOINT ["/entrypoint.sh"]
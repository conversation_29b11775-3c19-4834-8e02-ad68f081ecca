#!/bin/bash

# Optimized parallel Docker build script with advanced caching
set -e

echo "🚀 Starting optimized parallel Docker builds..."

# Enable BuildKit for better caching
export DOCKER_BUILDKIT=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to build with advanced cache
build_with_cache() {
    local dockerfile=$1
    local tag=$2
    local context=$3
    local app_name=$4
    
    echo -e "${BLUE}📦 Building $app_name...${NC}"
    
    # Use buildx for better caching and parallel builds
    docker buildx build \
        --file "$dockerfile" \
        --tag "$tag" \
        --cache-from "type=local,src=/tmp/.buildx-cache-$app_name" \
        --cache-to "type=local,dest=/tmp/.buildx-cache-$app_name,mode=max" \
        --load \
        "$context" && \
    echo -e "${GREEN}✅ Built $tag${NC}" || \
    echo -e "${RED}❌ Failed to build $tag${NC}"
}

# Create cache directories
mkdir -p /tmp/.buildx-cache-migrate /tmp/.buildx-cache-backend /tmp/.buildx-cache-frontend

# Start builds in parallel
echo -e "${YELLOW}🔧 Starting parallel builds...${NC}"

# Build migrate in background
(
    build_with_cache "apps/migrate/Dockerfile" "warda/migrate:latest" "." "migrate"
) &
MIGRATE_PID=$!

# Build backend in background  
(
    build_with_cache "apps/backend/Dockerfile" "warda/backend:latest" "." "backend"
) &
BACKEND_PID=$!

# Build frontend in background
(
    build_with_cache "apps/frontend/Dockerfile" "warda/frontend:latest" "." "frontend"
) &
FRONTEND_PID=$!

# Wait for all builds to complete
echo -e "${YELLOW}⏳ Waiting for builds to complete...${NC}"

wait $MIGRATE_PID
MIGRATE_EXIT=$?

wait $BACKEND_PID  
BACKEND_EXIT=$?

wait $FRONTEND_PID
FRONTEND_EXIT=$?

# Check results
echo ""
echo -e "${BLUE}📋 Build Results:${NC}"

if [ $MIGRATE_EXIT -eq 0 ]; then
    echo -e "${GREEN}✅ Migrate: SUCCESS${NC}"
else
    echo -e "${RED}❌ Migrate: FAILED${NC}"
fi

if [ $BACKEND_EXIT -eq 0 ]; then
    echo -e "${GREEN}✅ Backend: SUCCESS${NC}"
else
    echo -e "${RED}❌ Backend: FAILED${NC}"
fi

if [ $FRONTEND_EXIT -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend: SUCCESS${NC}"
else
    echo -e "${RED}❌ Frontend: FAILED${NC}"
fi

# Overall result
if [ $MIGRATE_EXIT -eq 0 ] && [ $BACKEND_EXIT -eq 0 ] && [ $FRONTEND_EXIT -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 All builds completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}💡 Performance Tips:${NC}"
    echo "  - Cache directories created in /tmp/.buildx-cache-*"
    echo "  - Subsequent builds will be much faster"
    echo "  - Use 'docker system prune' to clean up if needed"
    exit 0
else
    echo ""
    echo -e "${RED}💥 Some builds failed. Check the output above.${NC}"
    exit 1
fi

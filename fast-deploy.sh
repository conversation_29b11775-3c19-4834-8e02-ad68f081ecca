#!/bin/bash

# Fast deployment script with all optimizations
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🚀 Fast Deployment Pipeline Starting...${NC}"
echo ""

# Function to print step headers
print_step() {
    echo -e "${BLUE}==== $1 ====${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_step "Checking Prerequisites"
if ! command_exists docker; then
    echo -e "${RED}❌ Docker not found${NC}"
    exit 1
fi

if ! command_exists helm; then
    echo -e "${RED}❌ Helm not found${NC}"
    exit 1
fi

if ! command_exists kubectl; then
    echo -e "${RED}❌ kubectl not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites found${NC}"
echo ""

# Enable BuildKit
export DOCKER_BUILDKIT=1

# Step 1: Parallel Docker Builds
print_step "Step 1: Parallel Docker Builds"
start_time=$(date +%s)

echo -e "${YELLOW}🔧 Starting parallel builds with advanced caching...${NC}"

# Create cache directories
mkdir -p /tmp/.buildx-cache-migrate /tmp/.buildx-cache-backend /tmp/.buildx-cache-frontend

# Start all builds in parallel
echo "📦 Building migrate..." &
docker buildx build \
    --file apps/migrate/Dockerfile \
    --tag warda/migrate:latest \
    --cache-from type=local,src=/tmp/.buildx-cache-migrate \
    --cache-to type=local,dest=/tmp/.buildx-cache-migrate,mode=max \
    --load \
    . > /tmp/migrate-build.log 2>&1 &
MIGRATE_PID=$!

echo "📦 Building backend..." &
docker buildx build \
    --file apps/backend/Dockerfile \
    --tag warda/backend:latest \
    --cache-from type=local,src=/tmp/.buildx-cache-backend \
    --cache-to type=local,dest=/tmp/.buildx-cache-backend,mode=max \
    --load \
    . > /tmp/backend-build.log 2>&1 &
BACKEND_PID=$!

echo "📦 Building frontend..." &
docker buildx build \
    --file apps/frontend/Dockerfile \
    --tag warda/frontend:latest \
    --cache-from type=local,src=/tmp/.buildx-cache-frontend \
    --cache-to type=local,dest=/tmp/.buildx-cache-frontend,mode=max \
    --load \
    . > /tmp/frontend-build.log 2>&1 &
FRONTEND_PID=$!

# Wait for all builds with progress indication
echo -e "${YELLOW}⏳ Waiting for parallel builds to complete...${NC}"

wait $MIGRATE_PID
MIGRATE_EXIT=$?
if [ $MIGRATE_EXIT -eq 0 ]; then
    echo -e "${GREEN}✅ Migrate build completed${NC}"
else
    echo -e "${RED}❌ Migrate build failed${NC}"
    cat /tmp/migrate-build.log
    exit 1
fi

wait $BACKEND_PID
BACKEND_EXIT=$?
if [ $BACKEND_EXIT -eq 0 ]; then
    echo -e "${GREEN}✅ Backend build completed${NC}"
else
    echo -e "${RED}❌ Backend build failed${NC}"
    cat /tmp/backend-build.log
    exit 1
fi

wait $FRONTEND_PID
FRONTEND_EXIT=$?
if [ $FRONTEND_EXIT -eq 0 ]; then
    echo -e "${GREEN}✅ Frontend build completed${NC}"
else
    echo -e "${RED}❌ Frontend build failed${NC}"
    cat /tmp/frontend-build.log
    exit 1
fi

build_end_time=$(date +%s)
build_duration=$((build_end_time - start_time))
echo -e "${GREEN}🎉 All builds completed in ${build_duration}s${NC}"
echo ""

# Step 2: Import to k3s (if available)
if command_exists k3s; then
    print_step "Step 2: Import Images to k3s"
    import_start_time=$(date +%s)
    
    echo -e "${YELLOW}📥 Importing images to k3s in parallel...${NC}"
    
    (docker save warda/migrate:latest | sudo k3s ctr images import -) &
    (docker save warda/backend:latest | sudo k3s ctr images import -) &
    (docker save warda/frontend:latest | sudo k3s ctr images import -) &
    
    wait
    
    import_end_time=$(date +%s)
    import_duration=$((import_end_time - import_start_time))
    echo -e "${GREEN}✅ Images imported to k3s in ${import_duration}s${NC}"
    echo ""
fi

# Step 3: Helm Deploy
print_step "Step 3: Optimized Helm Deployment"
deploy_start_time=$(date +%s)

echo -e "${YELLOW}🚀 Deploying with optimized Helm strategy...${NC}"

helm upgrade --install warda helm/warda \
    -f helm/warda/values.yaml \
    --timeout=300s \
    --wait \
    --wait-for-jobs

deploy_end_time=$(date +%s)
deploy_duration=$((deploy_end_time - deploy_start_time))
echo -e "${GREEN}✅ Deployment completed in ${deploy_duration}s${NC}"
echo ""

# Summary
total_end_time=$(date +%s)
total_duration=$((total_end_time - start_time))

print_step "Deployment Summary"
echo -e "${GREEN}🎉 Fast deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}📊 Performance Summary:${NC}"
echo -e "  Build Time:  ${build_duration}s"
if command_exists k3s; then
    echo -e "  Import Time: ${import_duration}s"
fi
echo -e "  Deploy Time: ${deploy_duration}s"
echo -e "  Total Time:  ${total_duration}s"
echo ""
echo -e "${PURPLE}💡 Optimization Features Used:${NC}"
echo "  ✅ Parallel Docker builds"
echo "  ✅ BuildKit cache mounts"
echo "  ✅ Advanced layer caching"
echo "  ✅ Parallel image imports"
echo "  ✅ Optimized rolling updates"
echo "  ✅ Helm wait strategies"
echo ""
echo -e "${GREEN}🚀 Your application is now deployed and ready!${NC}"

name: CI - Deploy Dev Cluster

on:
  push:
    branches: [dev]

jobs:
  build-and-deploy:
    runs-on: wardaserver

    env:
      IMAGE_PREFIX: warda
      REGISTRY: ghcr.io/sietse-doubleprecision

    steps:
      - name: Set KUBECONFIG env
        run: echo "KUBECONFIG=/home/<USER>/.kube/config" >> $GITHUB_ENV

      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - uses: docker/setup-buildx-action@v3.11.1
        with:
          driver-opts: image=moby/buildkit:v0.23.2

      - name: Setup cache directories
        run: |
          mkdir -p /tmp/.buildx-cache-migrate
          mkdir -p /tmp/.buildx-cache-backend
          mkdir -p /tmp/.buildx-cache-frontend

      - name: Build images in parallel
        run: |
          echo "🚀 Building all images in parallel..."

          # Start all builds in parallel with advanced caching
          docker buildx build \
            --file apps/migrate/Dockerfile \
            --tag $IMAGE_PREFIX/migrate:dev \
            --cache-from type=local,src=/tmp/.buildx-cache-migrate \
            --cache-to type=local,dest=/tmp/.buildx-cache-migrate,mode=max \
            --load \
            . &
          MIGRATE_PID=$!

          docker buildx build \
            --file apps/backend/Dockerfile \
            --tag $IMAGE_PREFIX/backend:dev \
            --cache-from type=local,src=/tmp/.buildx-cache-backend \
            --cache-to type=local,dest=/tmp/.buildx-cache-backend,mode=max \
            --load \
            . &
          BACKEND_PID=$!

          docker buildx build \
            --file apps/frontend/Dockerfile \
            --tag $IMAGE_PREFIX/frontend:dev \
            --cache-from type=local,src=/tmp/.buildx-cache-frontend \
            --cache-to type=local,dest=/tmp/.buildx-cache-frontend,mode=max \
            --load \
            . &
          FRONTEND_PID=$!

          # Wait for all builds
          wait $MIGRATE_PID && echo "✅ Migrate build complete"
          wait $BACKEND_PID && echo "✅ Backend build complete"
          wait $FRONTEND_PID && echo "✅ Frontend build complete"

          echo "🎉 All parallel builds completed!"

      - name: Import images to k3s in parallel
        run: |
          echo "📦 Importing images to k3s in parallel..."

          # Import all images in parallel for faster deployment
          (docker save $IMAGE_PREFIX/migrate:dev | sudo k3s ctr images import -) &
          (docker save $IMAGE_PREFIX/backend:dev | sudo k3s ctr images import -) &
          (docker save $IMAGE_PREFIX/frontend:dev | sudo k3s ctr images import -) &

          # Wait for all imports to complete
          wait

          # Verify images are available in k3s
          echo "✅ Images imported to k3s:"
          sudo k3s ctr images list | grep warda

      - uses: azure/setup-helm@v4.3.0
        with:
          version: 'v3.18.4'

      - name: Helm deploy with optimized strategy
        run: |
          echo "🚀 Deploying with optimized Helm strategy..."

          # Deploy with faster rollout strategy
          helm upgrade --install warda helm/warda \
            --namespace warda \
            --create-namespace \
            --set global.image.registry="" \
            --set global.image.repository=warda \
            --set global.image.tag=dev \
            --set global.image.pullPolicy=Never \
            --set backend.strategy.type=RollingUpdate \
            --set backend.strategy.rollingUpdate.maxUnavailable=0 \
            --set backend.strategy.rollingUpdate.maxSurge=1 \
            --set frontend.strategy.type=RollingUpdate \
            --set frontend.strategy.rollingUpdate.maxUnavailable=0 \
            --set frontend.strategy.rollingUpdate.maxSurge=1 \
            --timeout=300s \
            --wait

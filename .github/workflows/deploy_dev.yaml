name: CI - Deploy Dev Cluster

on:
  push:
    branches: [dev]

jobs:
  build-and-deploy:
    runs-on: wardaserver

    env:
      IMAGE_PREFIX: warda

    steps:
      - name: Set KUBECONFIG env
        run: echo "KUBECONFIG=/home/<USER>/.kube/config" >> $GITHUB_ENV

      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - uses: docker/setup-buildx-action@v3.11.1
        with:
          driver-opts: image=moby/buildkit:v0.23.2

      - name: Build images
        run: |
          echo "🛠️ Building all images..."

          # Build migrate
          docker buildx build \
            --file apps/migrate/Dockerfile \
            --tag $IMAGE_PREFIX/migrate:dev \
            --load \
            .

          # Build backend
          docker buildx build \
            --file apps/backend/Dockerfile \
            --tag $IMAGE_PREFIX/backend:dev \
            --load \
            .

          # Build frontend
          docker buildx build \
            --file apps/frontend/Dockerfile \
            --tag $IMAGE_PREFIX/frontend:dev \
            --load \
            .

      - name: Import images to k3s
        run: |
          echo "📦 Importing images to k3s..."

          # Save images to tar files and import to k3s
          docker save $IMAGE_PREFIX/migrate:dev | sudo k3s ctr images import -
          docker save $IMAGE_PREFIX/backend:dev | sudo k3s ctr images import -
          docker save $IMAGE_PREFIX/frontend:dev | sudo k3s ctr images import -

          # Verify images are available in k3s
          echo "✅ Images imported to k3s:"
          sudo k3s ctr images list | grep warda

      - uses: azure/setup-helm@v4.3.0
        with:
          version: 'v3.18.4'

      - name: Helm deploy
        run: |
          helm upgrade --install warda helm/warda \
            --namespace warda \
            --create-namespace \
            --set global.image.registry="" \
            --set global.image.repository=warda \
            --set global.image.tag=dev \
            --set global.image.pullPolicy=Never
